/* content.css - 主要内容区域样式 */

/* 内容头部 */
.content-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start; /* 顶部对齐 */
    padding: 12px 24px; /* 稍微减小内边距 */
    border-bottom: 1px solid #e0e0e0;
    background-color: #fafafa;
    flex-shrink: 0; /* 防止标题收缩 */
}

/* 左侧控件包装(切换按钮 + 标题区) */
.header-controls-left {
    display: flex;
    align-items: center; /* 垂直对齐按钮和标题 */
    gap: 12px; /* 切换按钮和标题间的间距 */
    /* 允许此区域增长，但防止标题推动操作按钮 */
    flex-grow: 1;
    min-width: 0; /* 对包含文本的flex项目很重要 */
}

.header-title-section {
    /* 允许标题部分占用可用空间 */
    flex-grow: 1;
    min-width: 0; /* 防止溢出问题 */
}

.header-title-section h1 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 2px; /* 减小边距 */
    color: #1f1f1f;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.knowledge-suggestion {
    font-size: 12px;
    color: #666;
    display: flex;
    align-items: center;
    cursor: pointer;
    white-space: nowrap;
}

.knowledge-suggestion .icon-lightbulb {
    margin-right: 4px;
    color: #fbbc05; /* 黄色灯泡图标 */
}

.knowledge-suggestion .badge {
    background-color: #e8f0fe;
    color: #1967d2;
    padding: 1px 5px;
    border-radius: 4px;
    margin-left: 6px;
    font-weight: 500;
    font-size: 10px;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 8px; /* 按钮间距 */
    flex-shrink: 0; /* 防止操作按钮换行 */
    margin-top: 2px; /* 与标题对齐（如需要） */
}

.action-btn {
    background: none;
    border: 1px solid #dcdcdc;
    border-radius: 4px;
    padding: 5px 8px; /* 统一内边距 */
    cursor: pointer;
    font-size: 13px;
    color: #555;
    display: flex;
    align-items: center;
    gap: 4px;
    height: 30px; /* 固定高度以对齐 */
    box-sizing: border-box;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background-color: #f5f5f5;
    border-color: #ccc;
}

/* 专门为切换按钮的样式 */
.toggle-btn {
    padding: 5px; /* 使切换按钮更方形 */
    width: 30px;
    justify-content: center; /* 居中图标 */
}

.toggle-btn span {
    font-size: 16px;
    line-height: 1; /* 确保图标垂直对齐 */
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.toggle-btn:hover {
    background-color: #f0f0f0;
    transform: scale(1.05);
}

/* 调整聊天区域 */
.chat-area {
    flex-grow: 1;
    overflow-y: auto; /* 聊天区可滚动 */
    padding: 20px 24px;
}
