var Te=Object.defineProperty;var Ae=(L,o,t)=>o in L?Te(L,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):L[o]=t;var pe=(L,o,t)=>Ae(L,typeof o!="symbol"?o+"":o,t);import{_ as ne,I as B}from"./_plugin-vue_export-helper-DCajJDzs.js";import{d as ae,c as d,o as l,e,t as h,r as O,y as ie,m as q,f as ye,h as A,b as V,q as E,w as N,v as Y,F as K,i as J,p as Q,C as xe,a as re,A as oe,T as _e,D as Ee,u as G,E as $e,s as he,x as fe,G as Ve,j as Me,g as Pe,H as ke}from"./index-Dy79UgHd.js";const Ue={class:"switch"},Oe=["checked"],Be={class:"switch-label"},Ge=ae({__name:"index",props:{enabled:{type:Boolean},label:{}},emits:["update:switchValue"],setup(L,{emit:o}){const t=o,_=p=>{const i=p.target.checked;t("update:switchValue",i)};return(p,i)=>(l(),d("label",Ue,[e("input",{type:"checkbox",checked:p.enabled,onChange:_},null,40,Oe),i[0]||(i[0]=e("span",{class:"slider"},null,-1)),e("span",Be,h(p.label),1)]))}}),Le=ne(Ge,[["__scopeId","data-v-d484b4a3"]]);class ue{static async getConfigsByGroup(o){try{const t=await fetch(`${this.BASE_URL}/group/${o}`);if(!t.ok)throw new Error(`获取${o}组配置失败: ${t.status}`);return await t.json()}catch(t){throw console.error(`获取${o}组配置失败:`,t),t}}static async batchUpdateConfigs(o){if(!o||o.length===0)return{success:!0,message:"没有需要更新的配置"};try{const t=await fetch(`${this.BASE_URL}/batch-update`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)});if(!t.ok)throw new Error(`批量更新配置失败: ${t.status}`);return{success:!0,message:"配置保存成功"}}catch(t){return console.error("批量更新配置失败:",t),{success:!1,message:t instanceof Error?t.message:"更新失败，请重试"}}}static async getConfigById(o){try{const t=await fetch(`${this.BASE_URL}/${o}`);if(!t.ok)throw new Error(`获取配置项失败: ${t.status}`);return await t.json()}catch(t){throw console.error(`获取配置项[${o}]失败:`,t),t}}static async updateConfig(o){try{const t=await fetch(`${this.BASE_URL}/${o.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)});if(!t.ok)throw new Error(`更新配置项失败: ${t.status}`);return{success:!0,message:"配置更新成功"}}catch(t){return console.error("更新配置项失败:",t),{success:!1,message:t instanceof Error?t.message:"更新失败，请重试"}}}}pe(ue,"BASE_URL","/api/config");const Ie={class:"config-panel"},Re={class:"config-header"},Ne={class:"header-left"},De={class:"config-stats"},je={class:"stat-item"},Ke={class:"stat-value"},Je={key:0,class:"stat-item"},qe={class:"stat-value modified"},ze={class:"header-right"},Fe={class:"import-export-actions"},Ye={class:"action-btn",title:"导入配置"},He={class:"search-box"},Xe={key:0,class:"loading-container"},Qe={key:1,class:"config-groups"},We={class:"group-header"},Ze={class:"group-info"},es={class:"group-icon"},ss={class:"group-actions"},ts=["onClick","disabled"],os={class:"sub-groups"},ns=["onClick"],as={class:"sub-group-info"},ls={class:"sub-group-title"},is={class:"item-count"},rs={class:"config-items"},cs={key:0,class:"config-item-content vertical-layout"},ds={class:"config-item-info"},us={class:"config-item-header"},ps={class:"config-label"},vs={class:"type-badge boolean"},gs={key:0,class:"modified-badge"},fs=["title"],hs={class:"config-control"},ms=["value","onChange"],ys=["value"],_s={key:1,class:"config-item-content vertical-layout"},bs={class:"config-item-info"},ws={class:"config-item-header"},$s={class:"config-label"},ks={key:0,class:"modified-badge"},Cs=["title"],Ss={class:"config-control"},Ts=["value","onChange"],As=["value"],xs={key:2,class:"config-item-content vertical-layout"},Es={class:"config-item-info"},Vs={class:"config-item-header"},Ms={class:"config-label"},Ps={key:0,class:"modified-badge"},Us=["title"],Os={class:"config-control"},Bs=["value","onInput"],Gs={key:3,class:"config-item-content vertical-layout"},Ls={class:"config-item-info"},Is={class:"config-item-header"},Rs={class:"config-label"},Ns={key:0,class:"modified-badge"},Ds=["title"],js={key:0,class:"config-meta"},Ks={class:"range-info"},Js={class:"config-control"},qs=["value","onInput","min","max"],zs={key:4,class:"config-item-content vertical-layout"},Fs={class:"config-item-info"},Ys={class:"config-item-header"},Hs={class:"config-label"},Xs={class:"type-badge string"},Qs={key:0,class:"modified-badge"},Ws=["title"],Zs={class:"config-control"},et=["value","onInput"],st={key:2,class:"empty-state"},tt=ae({__name:"basicConfig",setup(L){const o=O(!0),t=O(!1),_=O([]),p=O(new Map),i=O(new Set),S=ie({show:!1,text:"",type:"success"}),M=O(""),C={maxSteps:"智能体执行最大步数",resetAllAgents:"重置所有agent",headlessBrowser:"是否使用无头浏览器模式",browserTimeout:"浏览器请求超时时间(秒)",browserDebug:"浏览器debug模式",autoOpenBrowser:"启动时自动打开浏览器",consoleInteractive:"启用控制台交互模式",systemName:"系统名称",language:"默认语言",maxThreads:"最大线程数",timeoutSeconds:"请求超时时间(秒)"},I={manus:"智能体设置",browser:"浏览器设置",interaction:"交互设置",system:"系统设置",performance:"性能设置"},x={manus:"🤖",browser:"🌐",interaction:"🖥️",system:"⚙️",performance:"⚡"},D={agent:"智能体设置",browser:"浏览器设置",interaction:"交互设置",system:"系统设置",performance:"性能设置",general:"常规设置"},ee=q(()=>_.value.some(c=>c.subGroups.some(r=>r.items.some(y=>p.value.get(y.id)!==y.configValue)))),U=c=>c==="true",T=c=>parseFloat(c)||0,v=c=>({maxSteps:1,browserTimeout:1,maxThreads:1,timeoutSeconds:5})[c]||1,P=c=>({maxSteps:100,browserTimeout:600,maxThreads:32,timeoutSeconds:300})[c]||1e4,H=c=>typeof c=="string"?c:c.value,le=c=>typeof c=="string"?c:c.label,ce=(c,r)=>{if(typeof r=="boolean")return r.toString();if(typeof r=="string"){if(c.options&&c.options.length>0){const y=c.options.find(m=>(typeof m=="string"?m:m.label)===r||(typeof m=="string"?m:m.value)===r);if(y)return typeof y=="string"?y:y.value}return r}return String(r)},z=(c,r,y=!1)=>{let m;c.inputType==="BOOLEAN"||c.inputType==="CHECKBOX"?m=ce(c,r):m=String(r),c.configValue!==m&&(c.configValue=m,c._modified=!0,(y||c.inputType==="BOOLEAN"||c.inputType==="CHECKBOX"||c.inputType==="SELECT")&&f())};let se=null;const f=()=>{se&&clearTimeout(se),se=window.setTimeout(()=>{u()},500)},a=(c,r="success")=>{S.text=c,S.type=r,S.show=!0,setTimeout(()=>{S.show=!1},3e3)},b=async()=>{try{o.value=!0;const r=["manus","browser","interaction","system","performance"].map(async m=>{try{const n=await ue.getConfigsByGroup(m);if(n.length===0)return null;const $=n.map(R=>({...R,displayName:R.description||C[R.configKey]||R.configKey,min:v(R.configKey),max:P(R.configKey)}));$.forEach(R=>{p.value.set(R.id,R.configValue)});const k=new Map;$.forEach(R=>{const F=R.configSubGroup||"general";k.has(F)||k.set(F,[]),k.get(F).push(R)});const j=Array.from(k.entries()).map(([R,F])=>({name:R,displayName:D[R]||R,items:F}));return{name:m,displayName:I[m]||m,subGroups:j}}catch(n){return console.warn(`加载配置组 ${m} 失败，跳过:`,n),null}}),y=await Promise.all(r);_.value=y.filter(m=>m!==null),console.log("配置加载完成:",_.value)}catch(c){console.error("加载配置失败:",c),a("加载配置失败，请刷新重试","error")}finally{o.value=!1}},u=async()=>{if(!(t.value||!ee.value))try{t.value=!0;const c=[];if(_.value.forEach(y=>{y.subGroups.forEach(m=>{const n=m.items.filter($=>$._modified);c.push(...n)})}),c.length===0){a("没有需要保存的修改");return}const r=await ue.batchUpdateConfigs(c);r.success?(c.forEach(y=>{p.value.set(y.id,y.configValue),y._modified=!1}),a("配置保存成功")):a(r.message||"保存失败","error")}catch(c){console.error("保存配置失败:",c),a("保存失败，请重试","error")}finally{t.value=!1}},s=async c=>{if(confirm(`确定要重置 "${I[c]||c}" 组的所有配置吗？`))try{t.value=!0;const y=_.value.find($=>$.name===c);if(!y)return;const m=[];if(y.subGroups.forEach($=>{$.items.forEach(k=>{const j=w(k.configKey);j!==k.configValue&&m.push({...k,configValue:j})})}),m.length===0){a("该组配置已是默认值");return}const n=await ue.batchUpdateConfigs(m);n.success?(await b(),a(`成功重置 ${m.length} 项配置`)):a(n.message||"重置失败","error")}catch(y){console.error("重置组配置失败:",y),a("重置失败，请重试","error")}finally{t.value=!1}},w=c=>({systemName:"JTaskPilot",language:"zh-CN",maxThreads:"8",timeoutSeconds:"60",autoOpenBrowser:"false",headlessBrowser:"true"})[c]||"",g=(c,r)=>{const y=`${c}-${r}`;i.value.has(y)?i.value.delete(y):i.value.add(y)},W=(c,r)=>i.value.has(`${c}-${r}`),X=q(()=>{const c=_.value.reduce((y,m)=>y+m.subGroups.reduce((n,$)=>n+$.items.length,0),0),r=_.value.reduce((y,m)=>y+m.subGroups.reduce((n,$)=>n+$.items.filter(k=>p.value.get(k.id)!==k.configValue).length,0),0);return{total:c,modified:r}}),de=q(()=>{if(!M.value.trim())return _.value;const c=M.value.toLowerCase();return _.value.map(r=>({...r,subGroups:r.subGroups.map(y=>({...y,items:y.items.filter(m=>m.displayName.toLowerCase().includes(c)||m.configKey.toLowerCase().includes(c)||m.description&&m.description.toLowerCase().includes(c))})).filter(y=>y.items.length>0)})).filter(r=>r.subGroups.length>0)}),te=()=>{try{const c={timestamp:new Date().toISOString(),version:"1.0",configs:_.value.reduce((n,$)=>($.subGroups.forEach(k=>{k.items.forEach(j=>{n[j.configKey]=j.configValue})}),n),{})},r=JSON.stringify(c,null,2),y=new Blob([r],{type:"application/json"}),m=document.createElement("a");m.href=URL.createObjectURL(y),m.download=`config-export-${new Date().toISOString().split("T")[0]}.json`,m.click(),a("配置导出成功")}catch(c){console.error("导出配置失败:",c),a("导出失败","error")}},be=c=>{var n;const r=c.target,y=(n=r.files)==null?void 0:n[0];if(!y)return;const m=new FileReader;m.onload=async $=>{var k;try{const j=JSON.parse((k=$.target)==null?void 0:k.result);if(!j.configs)throw new Error("无效的配置文件格式");if(!confirm("确定要导入配置吗？这将覆盖当前配置。"))return;t.value=!0;const F=[];if(_.value.forEach(Ce=>{Ce.subGroups.forEach(Se=>{Se.items.forEach(ge=>{j.configs.hasOwnProperty(ge.configKey)&&F.push({...ge,configValue:j.configs[ge.configKey]})})})}),F.length===0){a("没有找到可导入的配置项");return}const we=await ue.batchUpdateConfigs(F);we.success?(await b(),a(`成功导入 ${F.length} 项配置`)):a(we.message||"导入失败","error")}catch(j){console.error("导入配置失败:",j),a("导入失败，请检查文件格式","error")}finally{t.value=!1,r.value=""}},m.readAsText(y)};return ye(()=>{b()}),(c,r)=>(l(),d("div",Ie,[e("div",Re,[e("div",Ne,[r[3]||(r[3]=e("h2",null,"基础配置",-1)),e("div",De,[e("span",je,[r[1]||(r[1]=e("span",{class:"stat-label"},"总配置项:",-1)),e("span",Ke,h(X.value.total),1)]),X.value.modified>0?(l(),d("span",Je,[r[2]||(r[2]=e("span",{class:"stat-label"},"已修改:",-1)),e("span",qe,h(X.value.modified),1)])):V("",!0)])]),e("div",ze,[e("div",Fe,[e("button",{onClick:te,class:"action-btn",title:"导出配置"}," 📤 "),e("label",Ye,[r[4]||(r[4]=E(" 📥 ")),e("input",{type:"file",accept:".json",onChange:be,style:{display:"none"}},null,32)])]),e("div",He,[N(e("input",{"onUpdate:modelValue":r[0]||(r[0]=y=>M.value=y),type:"text",placeholder:"搜索配置项...",class:"search-input"},null,512),[[Y,M.value]]),r[5]||(r[5]=e("span",{class:"search-icon"},"🔍",-1))])])]),o.value?(l(),d("div",Xe,r[6]||(r[6]=[e("div",{class:"loading-spinner"},null,-1),e("p",null,"正在加载配置...",-1)]))):de.value.length>0?(l(),d("div",Qe,[(l(!0),d(K,null,J(de.value,y=>(l(),d("div",{key:y.name,class:"config-group"},[e("div",We,[e("div",Ze,[e("span",es,h(x[y.name]||"⚙️"),1)]),e("div",ss,[e("button",{onClick:m=>s(y.name),class:"reset-btn",disabled:t.value,title:"重置该组所有配置为默认值"}," 重置 ",8,ts)]),r[7]||(r[7]=e("div",{class:"group-divider"},null,-1))]),e("div",os,[(l(!0),d(K,null,J(y.subGroups,m=>(l(),d("div",{key:m.name,class:"sub-group"},[e("div",{class:"sub-group-header",onClick:n=>g(y.name,m.name)},[e("div",as,[r[8]||(r[8]=e("span",{class:"sub-group-icon"},"📁",-1)),e("h4",ls,h(m.displayName),1),e("span",is,"("+h(m.items.length)+")",1)]),e("span",{class:Q(["collapse-icon",{collapsed:W(y.name,m.name)}])}," ▼ ",2)],8,ns),N(e("div",rs,[(l(!0),d(K,null,J(m.items,n=>(l(),d("div",{key:n.id,class:Q(["config-item",{modified:p.value.get(n.id)!==n.configValue}])},[n.inputType==="BOOLEAN"||n.inputType==="CHECKBOX"?(l(),d("div",cs,[e("div",ds,[e("div",us,[e("label",ps,[E(h(n.description||n.displayName)+" ",1),e("span",vs,h(n.inputType==="CHECKBOX"?"选择":"布尔"),1),p.value.get(n.id)!==n.configValue?(l(),d("span",gs,"已修改")):V("",!0)]),e("span",{class:"config-key",title:n.configKey},h(n.configKey),9,fs)])]),e("div",hs,[n.options&&n.options.length>0?(l(),d("select",{key:0,class:"config-input select-input",value:n.configValue,onChange:$=>{var k;return z(n,((k=$.target)==null?void 0:k.value)||"")}},[(l(!0),d(K,null,J(n.options,$=>(l(),d("option",{key:H($),value:H($)},h(le($)),9,ys))),128))],40,ms)):(l(),re(Le,{key:1,enabled:U(n.configValue),label:"","onUpdate:switchValue":$=>z(n,$)},null,8,["enabled","onUpdate:switchValue"]))])])):n.inputType==="SELECT"?(l(),d("div",_s,[e("div",bs,[e("div",ws,[e("label",$s,[E(h(n.description||n.displayName)+" ",1),r[9]||(r[9]=e("span",{class:"type-badge select"},"选择",-1)),p.value.get(n.id)!==n.configValue?(l(),d("span",ks,"已修改")):V("",!0)]),e("span",{class:"config-key",title:n.configKey},h(n.configKey),9,Cs)])]),e("div",Ss,[e("select",{class:"config-input select-input",value:n.configValue,onChange:$=>{var k;return z(n,((k=$.target)==null?void 0:k.value)||"")}},[(l(!0),d(K,null,J(n.options||[],$=>(l(),d("option",{key:H($),value:H($)},h(le($)),9,As))),128))],40,Ts)])])):n.inputType==="TEXTAREA"?(l(),d("div",xs,[e("div",Es,[e("div",Vs,[e("label",Ms,[E(h(n.description||n.displayName)+" ",1),r[10]||(r[10]=e("span",{class:"type-badge textarea"},"多行",-1)),p.value.get(n.id)!==n.configValue?(l(),d("span",Ps,"已修改")):V("",!0)]),e("span",{class:"config-key",title:n.configKey},h(n.configKey),9,Us)])]),e("div",Os,[e("textarea",{class:"config-input textarea-input",value:n.configValue,onInput:$=>{var k;return z(n,((k=$.target)==null?void 0:k.value)||"")},onBlur:f,rows:"3"},null,40,Bs)])])):n.inputType==="NUMBER"?(l(),d("div",Gs,[e("div",Ls,[e("div",Is,[e("label",Rs,[E(h(n.description||n.displayName)+" ",1),r[11]||(r[11]=e("span",{class:"type-badge number"},"数值",-1)),p.value.get(n.id)!==n.configValue?(l(),d("span",Ns,"已修改")):V("",!0)]),e("span",{class:"config-key",title:n.configKey},h(n.configKey),9,Ds),n.min||n.max?(l(),d("div",js,[e("span",Ks," 范围: "+h(n.min||0)+" - "+h(n.max||"∞"),1)])):V("",!0)])]),e("div",Js,[e("input",{type:"number",class:"config-input number-input",value:T(n.configValue),onInput:$=>{var k;return z(n,((k=$.target)==null?void 0:k.value)||"")},onBlur:f,min:n.min||1,max:n.max||1e4},null,40,qs)])])):(l(),d("div",zs,[e("div",Fs,[e("div",Ys,[e("label",Hs,[E(h(n.description||n.displayName)+" ",1),e("span",Xs,h(n.inputType==="TEXT"?"文本":"字符串"),1),p.value.get(n.id)!==n.configValue?(l(),d("span",Qs,"已修改")):V("",!0)]),e("span",{class:"config-key",title:n.configKey},h(n.configKey),9,Ws)])]),e("div",Zs,[e("input",{type:"text",class:"config-input text-input",value:n.configValue,onInput:$=>{var k;return z(n,((k=$.target)==null?void 0:k.value)||"")},onBlur:f},null,40,et)])]))],2))),128))],512),[[xe,!W(y.name,m.name)]])]))),128))])]))),128))])):(l(),d("div",st,r[12]||(r[12]=[e("p",null,"未找到配置项",-1)]))),A(_e,{name:"message-fade"},{default:oe(()=>[S.show?(l(),d("div",{key:0,class:Q(["message-toast",S.type])},h(S.text),3)):V("",!0)]),_:1})]))}}),ot=ne(tt,[["__scopeId","data-v-64ca12a9"]]),nt={class:"modal-header"},at={class:"modal-content"},lt={class:"modal-footer"},it=ae({__name:"index",props:{modelValue:{type:Boolean,required:!0},title:{type:String,default:""}},emits:["update:modelValue","confirm"],setup(L){const o=t=>{t.target===t.currentTarget&&(t.stopPropagation(),t.preventDefault())};return(t,_)=>(l(),re(Ee,{to:"body"},[A(_e,{name:"modal"},{default:oe(()=>[L.modelValue?(l(),d("div",{key:0,class:"modal-overlay",onClick:o},[e("div",{class:"modal-container",onClick:_[3]||(_[3]=he(()=>{},["stop"]))},[e("div",nt,[e("h3",null,h(L.title),1),e("button",{class:"close-btn",onClick:_[0]||(_[0]=p=>t.$emit("update:modelValue",!1))},[A(G(B),{icon:"carbon:close"})])]),e("div",at,[$e(t.$slots,"default",{},void 0,!0)]),e("div",lt,[$e(t.$slots,"footer",{},()=>[e("button",{class:"cancel-btn",onClick:_[1]||(_[1]=p=>t.$emit("update:modelValue",!1))},"取消"),e("button",{class:"confirm-btn",onClick:_[2]||(_[2]=p=>t.$emit("confirm"))},"确认")],!0)])])])):V("",!0)]),_:3})]))}}),me=ne(it,[["__scopeId","data-v-9e83cb7f"]]),rt={class:"tool-selection-content"},ct={class:"tool-controls"},dt={class:"search-container"},ut={class:"sort-container"},pt={class:"tool-summary"},vt={class:"summary-text"},gt={key:0,class:"tool-groups"},ft=["onClick"],ht={class:"group-title-area"},mt={class:"group-name"},yt={class:"group-count"},_t={class:"group-enable-all"},bt=["checked","onChange","data-group"],wt={class:"tool-info"},$t={class:"tool-selection-name"},kt={key:0,class:"tool-selection-desc"},Ct={class:"tool-actions"},St=["checked","onChange"],Tt={key:1,class:"empty-state"},At=ae({__name:"index",props:{modelValue:{type:Boolean},tools:{},selectedToolIds:{}},emits:["update:modelValue","confirm"],setup(L,{emit:o}){const t=L,_=o,p=q({get:()=>t.modelValue,set:f=>_("update:modelValue",f)}),i=O(""),S=O("group"),M=O(new Set),C=O([]),I=(f,a)=>{const b=document.querySelector(`input[data-group="${f}"]`);b&&(b.indeterminate=H(a))};fe(()=>t.selectedToolIds,f=>{C.value=[...f]},{immediate:!0});const x=q(()=>{let f=t.tools.filter(a=>a&&a.key);if(i.value){const a=i.value.toLowerCase();f=f.filter(b=>b.name.toLowerCase().includes(a)||b.description.toLowerCase().includes(a)||b.serviceGroup&&b.serviceGroup.toLowerCase().includes(a))}switch(S.value){case"name":f=[...f].sort((a,b)=>a.name.localeCompare(b.name));break;case"enabled":f=[...f].sort((a,b)=>{const u=C.value.includes(a.key),s=C.value.includes(b.key);return u&&!s?-1:!u&&s?1:a.name.localeCompare(b.name)});break;case"group":default:f=[...f].sort((a,b)=>{const u=a.serviceGroup||"未分组",s=b.serviceGroup||"未分组";return u!==s?u.localeCompare(s):a.name.localeCompare(b.name)});break}return f}),D=q(()=>{const f=new Map;return x.value.forEach(a=>{const b=a.serviceGroup||"未分组";f.has(b)||f.set(b,[]),f.get(b).push(a)}),new Map([...f.entries()].sort())}),ee=q(()=>x.value.length);fe([C,D],()=>{Me(()=>{for(const[f,a]of D.value)I(f,a)})},{flush:"post",deep:!1});const U=f=>C.value.includes(f),T=(f,a)=>{a.stopPropagation();const u=a.target.checked;if(!f){console.error("toolKey is undefined, cannot proceed");return}u?C.value.includes(f)||(C.value=[...C.value,f]):C.value=C.value.filter(s=>s!==f)},v=f=>f.filter(a=>C.value.includes(a.key)),P=f=>f.length>0&&f.every(a=>C.value.includes(a.key)),H=f=>{const a=v(f).length;return a>0&&a<f.length},le=(f,a)=>{a.stopPropagation();const u=a.target.checked,s=f.map(w=>w.key);if(u){const w=[...C.value];s.forEach(g=>{w.includes(g)||w.push(g)}),C.value=w}else C.value=C.value.filter(w=>!s.includes(w))},ce=f=>{M.value.has(f)?M.value.delete(f):M.value.add(f)},z=()=>{_("confirm",[...C.value]),_("update:modelValue",!1)},se=()=>{C.value=[...t.selectedToolIds],_("update:modelValue",!1)};return fe(p,f=>{if(f){M.value.clear();const a=Array.from(D.value.keys());a.length>1&&a.slice(1).forEach(b=>{M.value.add(b)})}}),(f,a)=>(l(),re(me,{modelValue:p.value,"onUpdate:modelValue":[a[4]||(a[4]=b=>p.value=b),se],title:"选择工具",onConfirm:z},{default:oe(()=>[e("div",rt,[e("div",ct,[e("div",dt,[N(e("input",{"onUpdate:modelValue":a[0]||(a[0]=b=>i.value=b),type:"text",class:"search-input",placeholder:"搜索工具..."},null,512),[[Y,i.value]])]),e("div",ut,[N(e("select",{"onUpdate:modelValue":a[1]||(a[1]=b=>S.value=b),class:"sort-select"},a[5]||(a[5]=[e("option",{value:"group"},"按服务组排序",-1),e("option",{value:"name"},"按名称排序",-1),e("option",{value:"enabled"},"按启用状态排序",-1)]),512),[[Ve,S.value]])])]),e("div",pt,[e("span",vt," 共 "+h(D.value.size)+" 个服务组，"+h(ee.value)+" 个工具 (已选择 "+h(C.value.length)+" 个) ",1)]),D.value.size>0?(l(),d("div",gt,[(l(!0),d(K,null,J(D.value,([b,u])=>(l(),d("div",{key:b,class:"tool-group"},[e("div",{class:Q(["tool-group-header",{collapsed:M.value.has(b)}]),onClick:s=>ce(b)},[e("div",ht,[A(G(B),{icon:M.value.has(b)?"carbon:chevron-right":"carbon:chevron-down",class:"collapse-icon"},null,8,["icon"]),A(G(B),{icon:"carbon:folder",class:"group-icon"}),e("span",mt,h(b),1),e("span",yt," ("+h(v(u).length)+"/"+h(u.length)+") ",1)]),e("div",{class:"group-actions",onClick:a[2]||(a[2]=he(()=>{},["stop"]))},[e("label",_t,[e("input",{type:"checkbox",class:"group-enable-checkbox",checked:P(u),onChange:s=>le(u,s),"data-group":b},null,40,bt),a[6]||(a[6]=e("span",{class:"enable-label"},"启用全部",-1))])])],10,ft),e("div",{class:Q(["tool-group-content",{collapsed:M.value.has(b)}])},[(l(!0),d(K,null,J(u.filter(s=>s&&s.key),s=>(l(),d("div",{key:s.key,class:"tool-selection-item"},[e("div",wt,[e("div",$t,h(s.name),1),s.description?(l(),d("div",kt,h(s.description),1)):V("",!0)]),e("div",Ct,[e("label",{class:"tool-enable-switch",onClick:a[3]||(a[3]=he(()=>{},["stop"]))},[e("input",{type:"checkbox",class:"tool-enable-checkbox",checked:U(s.key),onChange:w=>T(s.key,w)},null,40,St),a[7]||(a[7]=e("span",{class:"tool-enable-slider"},null,-1))])])]))),128))],2)]))),128))])):(l(),d("div",Tt,[A(G(B),{icon:"carbon:tools",class:"empty-icon"}),a[8]||(a[8]=e("p",null,"没有找到工具",-1))]))])]),_:1},8,["modelValue"]))}}),xt=ne(At,[["__scopeId","data-v-81d0fa5c"]]);class Z{static async handleResponse(o){if(!o.ok)try{const t=await o.json();throw new Error(t.message||`API请求失败: ${o.status}`)}catch{throw new Error(`API请求失败: ${o.status} ${o.statusText}`)}return o}static async getAllAgents(){try{const o=await fetch(this.BASE_URL);return await(await this.handleResponse(o)).json()}catch(o){throw console.error("获取Agent列表失败:",o),o}}static async getAgentById(o){try{const t=await fetch(`${this.BASE_URL}/${o}`);return await(await this.handleResponse(t)).json()}catch(t){throw console.error(`获取Agent[${o}]详情失败:`,t),t}}static async createAgent(o){try{const t=await fetch(this.BASE_URL,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)});return await(await this.handleResponse(t)).json()}catch(t){throw console.error("创建Agent失败:",t),t}}static async updateAgent(o,t){try{const _=await fetch(`${this.BASE_URL}/${o}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});return await(await this.handleResponse(_)).json()}catch(_){throw console.error(`更新Agent[${o}]失败:`,_),_}}static async deleteAgent(o){try{const t=await fetch(`${this.BASE_URL}/${o}`,{method:"DELETE"});if(t.status===400)throw new Error("不能删除默认Agent");await this.handleResponse(t)}catch(t){throw console.error(`删除Agent[${o}]失败:`,t),t}}static async getAvailableTools(){try{const o=await fetch(`${this.BASE_URL}/tools`);return await(await this.handleResponse(o)).json()}catch(o){throw console.error("获取可用工具列表失败:",o),o}}}pe(Z,"BASE_URL","/api/agents");const Et={class:"config-panel"},Vt={class:"panel-header"},Mt={class:"panel-actions"},Pt=["disabled"],Ut={class:"agent-layout"},Ot={class:"agent-list"},Bt={class:"list-header"},Gt={class:"agent-count"},Lt={key:0,class:"agents-container"},It=["onClick"],Rt={class:"agent-card-header"},Nt={class:"agent-name"},Dt={class:"agent-desc"},jt={key:0,class:"agent-tools"},Kt={key:0,class:"tool-more"},Jt={key:1,class:"loading-state"},qt={key:2,class:"empty-state"},zt={key:0,class:"agent-detail"},Ft={class:"detail-header"},Yt={class:"detail-actions"},Ht={class:"form-item"},Xt={class:"form-item"},Qt={class:"form-item"},Wt={class:"tools-section"},Zt={class:"assigned-tools"},eo={class:"section-header"},so={class:"tools-grid"},to={class:"tool-info"},oo={class:"tool-name"},no={class:"tool-desc"},ao={key:0,class:"no-tools"},lo={key:1,class:"no-selection"},io={class:"modal-form"},ro={class:"form-item"},co={class:"form-item"},uo={class:"form-item"},po={class:"delete-confirm"},vo=ae({__name:"agentConfig",setup(L){const o=O(!1),t=O(""),_=O(""),p=ie([]),i=O(null),S=ie([]),M=O(!1),C=O(!1),I=O(!1),x=ie({name:"",description:"",nextStepPrompt:""});q(()=>!i.value||!i.value.availableTools||!Array.isArray(i.value.availableTools)?S:S.filter(u=>!i.value.availableTools.includes(u.key)));const D=u=>{const s=S.find(w=>w.key===u);return s?s.name:u},ee=u=>{const s=S.find(w=>w.key===u);return s?s.description:""},U=(u,s)=>{s==="success"?(_.value=u,setTimeout(()=>{_.value=""},3e3)):(t.value=u,setTimeout(()=>{t.value=""},5e3))},T=async()=>{o.value=!0;try{const[u,s]=await Promise.all([Z.getAllAgents(),Z.getAvailableTools()]),w=u.map(g=>({...g,availableTools:Array.isArray(g.availableTools)?g.availableTools:[]}));p.splice(0,p.length,...w),S.splice(0,S.length,...s),w.length>0&&await v(w[0])}catch(u){console.error("加载数据失败:",u),U("加载数据失败: "+u.message,"error");const s=[{key:"search-web",name:"网络搜索",description:"在互联网上搜索信息",enabled:!0,serviceGroup:"搜索服务"},{key:"search-local",name:"本地搜索",description:"在本地文件中搜索内容",enabled:!0,serviceGroup:"搜索服务"},{key:"file-read",name:"读取文件",description:"读取本地或远程文件内容",enabled:!0,serviceGroup:"文件服务"},{key:"file-write",name:"写入文件",description:"创建或修改文件内容",enabled:!0,serviceGroup:"文件服务"},{key:"file-delete",name:"删除文件",description:"删除指定的文件",enabled:!1,serviceGroup:"文件服务"},{key:"calculator",name:"计算器",description:"执行数学计算",enabled:!0,serviceGroup:"计算服务"},{key:"code-execute",name:"代码执行",description:"执行Python或JavaScript代码",enabled:!0,serviceGroup:"计算服务"},{key:"weather",name:"天气查询",description:"获取指定地区的天气信息",enabled:!0,serviceGroup:"信息服务"},{key:"currency",name:"汇率查询",description:"查询货币汇率信息",enabled:!0,serviceGroup:"信息服务"},{key:"email",name:"发送邮件",description:"发送电子邮件",enabled:!1,serviceGroup:"通信服务"},{key:"sms",name:"发送短信",description:"发送短信消息",enabled:!1,serviceGroup:"通信服务"}],w=[{id:"demo-1",name:"通用助手",description:"一个能够处理各种任务的智能助手",nextStepPrompt:"You are a helpful assistant that can answer questions and help with various tasks. What would you like me to help you with next?",availableTools:["search-web","calculator","weather"]},{id:"demo-2",name:"数据分析师",description:"专门用于数据分析和可视化的Agent",nextStepPrompt:"You are a data analyst assistant specialized in analyzing data and creating visualizations. Please provide the data you would like me to analyze.",availableTools:["file-read","file-write","calculator","code-execute"]}];S.splice(0,S.length,...s),p.splice(0,p.length,...w),w.length>0&&(i.value=w[0])}finally{o.value=!1}},v=async u=>{if(u)try{const s=await Z.getAgentById(u.id);i.value={...s,availableTools:Array.isArray(s.availableTools)?s.availableTools:[]}}catch(s){console.error("加载Agent详情失败:",s),U("加载Agent详情失败: "+s.message,"error"),i.value={...u,availableTools:Array.isArray(u.availableTools)?u.availableTools:[]}}},P=()=>{x.name="",x.description="",x.nextStepPrompt="",M.value=!0},H=async()=>{var u;if(!x.name.trim()||!x.description.trim()){U("请填写必要的字段","error");return}try{const s={name:x.name.trim(),description:x.description.trim(),nextStepPrompt:((u=x.nextStepPrompt)==null?void 0:u.trim())||"",availableTools:[]},w=await Z.createAgent(s);p.push(w),i.value=w,M.value=!1,U("Agent创建成功","success")}catch(s){U("创建Agent失败: "+s.message,"error")}},le=()=>{I.value=!0},ce=u=>{i.value&&(i.value.availableTools=[...u])},z=async()=>{if(i.value){if(!i.value.name.trim()||!i.value.description.trim()){U("请填写必要的字段","error");return}try{const u=await Z.updateAgent(i.value.id,i.value),s=p.findIndex(w=>w.id===u.id);s!==-1&&(p[s]=u),i.value=u,U("Agent保存成功","success")}catch(u){U("保存Agent失败: "+u.message,"error")}}},se=()=>{C.value=!0},f=async()=>{if(i.value)try{await Z.deleteAgent(i.value.id);const u=p.findIndex(s=>s.id===i.value.id);u!==-1&&p.splice(u,1),i.value=p.length>0?p[0]:null,C.value=!1,U("Agent删除成功","success")}catch(u){U("删除Agent失败: "+u.message,"error")}},a=()=>{const u=document.createElement("input");u.type="file",u.accept=".json",u.onchange=s=>{var g;const w=(g=s.target.files)==null?void 0:g[0];if(w){const W=new FileReader;W.onload=async X=>{var de;try{const te=JSON.parse((de=X.target)==null?void 0:de.result);if(!te.name||!te.description)throw new Error("Agent配置格式不正确：缺少必要字段");const{id:be,...c}=te,r=await Z.createAgent(c);p.push(r),i.value=r,U("Agent导入成功","success")}catch(te){U("导入Agent失败: "+te.message,"error")}},W.readAsText(w)}},u.click()},b=()=>{if(i.value)try{const u=JSON.stringify(i.value,null,2),s=new Blob([u],{type:"application/json"}),w=URL.createObjectURL(s),g=document.createElement("a");g.href=w,g.download=`agent-${i.value.name}-${new Date().toISOString().split("T")[0]}.json`,g.click(),URL.revokeObjectURL(w),U("Agent导出成功","success")}catch(u){U("导出Agent失败: "+u.message,"error")}};return ye(()=>{T()}),(u,s)=>{var w;return l(),d("div",Et,[e("div",Vt,[s[14]||(s[14]=e("h2",null,"Agent配置",-1)),e("div",Mt,[e("button",{class:"action-btn",onClick:a},[A(G(B),{icon:"carbon:upload"}),s[12]||(s[12]=E(" 导入 "))]),e("button",{class:"action-btn",onClick:b,disabled:!i.value},[A(G(B),{icon:"carbon:download"}),s[13]||(s[13]=E(" 导出 "))],8,Pt)])]),e("div",Ut,[e("div",Ot,[e("div",Bt,[s[15]||(s[15]=e("h3",null,"已配置的Agent",-1)),e("span",Gt,"("+h(p.length)+")",1)]),o.value?V("",!0):(l(),d("div",Lt,[(l(!0),d(K,null,J(p,g=>{var W;return l(),d("div",{key:g.id,class:Q(["agent-card",{active:((W=i.value)==null?void 0:W.id)===g.id}]),onClick:X=>v(g)},[e("div",Rt,[e("span",Nt,h(g.name),1),A(G(B),{icon:"carbon:chevron-right"})]),e("p",Dt,h(g.description),1),g.availableTools&&Array.isArray(g.availableTools)&&g.availableTools.length>0?(l(),d("div",jt,[(l(!0),d(K,null,J(g.availableTools.slice(0,3),X=>(l(),d("span",{key:X,class:"tool-tag"},h(D(X)),1))),128)),g.availableTools.length>3?(l(),d("span",Kt," +"+h(g.availableTools.length-3),1)):V("",!0)])):V("",!0)],10,It)}),128))])),o.value?(l(),d("div",Jt,[A(G(B),{icon:"carbon:loading",class:"loading-icon"}),s[16]||(s[16]=E(" 加载中... "))])):V("",!0),!o.value&&p.length===0?(l(),d("div",qt,[A(G(B),{icon:"carbon:bot",class:"empty-icon"}),s[17]||(s[17]=e("p",null,"暂无Agent配置",-1))])):V("",!0),e("button",{class:"add-btn",onClick:P},[A(G(B),{icon:"carbon:add"}),s[18]||(s[18]=E(" 新建Agent "))])]),i.value?(l(),d("div",zt,[e("div",Ft,[e("h3",null,h(i.value.name),1),e("div",Yt,[e("button",{class:"action-btn primary",onClick:z},[A(G(B),{icon:"carbon:save"}),s[19]||(s[19]=E(" 保存 "))]),e("button",{class:"action-btn danger",onClick:se},[A(G(B),{icon:"carbon:trash-can"}),s[20]||(s[20]=E(" 删除 "))])])]),e("div",Ht,[s[21]||(s[21]=e("label",null,[E("Agent名称 "),e("span",{class:"required"},"*")],-1)),N(e("input",{type:"text","onUpdate:modelValue":s[0]||(s[0]=g=>i.value.name=g),placeholder:"输入Agent名称",required:""},null,512),[[Y,i.value.name]])]),e("div",Xt,[s[22]||(s[22]=e("label",null,[E("描述 "),e("span",{class:"required"},"*")],-1)),N(e("textarea",{"onUpdate:modelValue":s[1]||(s[1]=g=>i.value.description=g),rows:"3",placeholder:"描述这个Agent的功能和用途",required:""},null,512),[[Y,i.value.description]])]),e("div",Qt,[s[23]||(s[23]=e("label",null,"Agent提示词（人设，要求，以及下一步动作的指导）",-1)),N(e("textarea",{"onUpdate:modelValue":s[2]||(s[2]=g=>i.value.nextStepPrompt=g),rows:"8",placeholder:"设置Agent的人设、要求以及下一步动作的指导..."},null,512),[[Y,i.value.nextStepPrompt]])]),e("div",Wt,[s[26]||(s[26]=e("h4",null,"工具配置",-1)),e("div",Zt,[e("div",eo,[e("span",null,"已分配工具 ("+h((i.value.availableTools||[]).length)+")",1),S.length>0?(l(),d("button",{key:0,class:"action-btn small",onClick:le},[A(G(B),{icon:"carbon:add"}),s[24]||(s[24]=E(" 添加/删除工具 "))])):V("",!0)]),e("div",so,[(l(!0),d(K,null,J(i.value.availableTools||[],g=>(l(),d("div",{key:g,class:"tool-item assigned"},[e("div",to,[e("span",oo,h(D(g)),1),e("span",no,h(ee(g)),1)])]))),128)),!i.value.availableTools||i.value.availableTools.length===0?(l(),d("div",ao,[A(G(B),{icon:"carbon:tool-box"}),s[25]||(s[25]=e("span",null,"暂无分配的工具",-1))])):V("",!0)])])])])):(l(),d("div",lo,[A(G(B),{icon:"carbon:bot",class:"placeholder-icon"}),s[27]||(s[27]=e("p",null,"请选择一个Agent进行配置",-1))]))]),A(me,{modelValue:M.value,"onUpdate:modelValue":s[6]||(s[6]=g=>M.value=g),title:"新建Agent",onConfirm:H},{default:oe(()=>[e("div",io,[e("div",ro,[s[28]||(s[28]=e("label",null,[E("Agent名称 "),e("span",{class:"required"},"*")],-1)),N(e("input",{type:"text","onUpdate:modelValue":s[3]||(s[3]=g=>x.name=g),placeholder:"输入Agent名称",required:""},null,512),[[Y,x.name]])]),e("div",co,[s[29]||(s[29]=e("label",null,[E("描述 "),e("span",{class:"required"},"*")],-1)),N(e("textarea",{"onUpdate:modelValue":s[4]||(s[4]=g=>x.description=g),rows:"3",placeholder:"描述这个Agent的功能和用途",required:""},null,512),[[Y,x.description]])]),e("div",uo,[s[30]||(s[30]=e("label",null,"Agent提示词（人设，要求，以及下一步动作的指导）",-1)),N(e("textarea",{"onUpdate:modelValue":s[5]||(s[5]=g=>x.nextStepPrompt=g),rows:"8",placeholder:"设置Agent的人设、要求以及下一步动作的指导..."},null,512),[[Y,x.nextStepPrompt]])])])]),_:1},8,["modelValue"]),A(xt,{modelValue:I.value,"onUpdate:modelValue":s[7]||(s[7]=g=>I.value=g),tools:S,"selected-tool-ids":((w=i.value)==null?void 0:w.availableTools)||[],onConfirm:ce},null,8,["modelValue","tools","selected-tool-ids"]),A(me,{modelValue:C.value,"onUpdate:modelValue":s[9]||(s[9]=g=>C.value=g),title:"删除确认"},{footer:oe(()=>[e("button",{class:"cancel-btn",onClick:s[8]||(s[8]=g=>C.value=!1)},"取消"),e("button",{class:"confirm-btn danger",onClick:f},"删除")]),default:oe(()=>{var g;return[e("div",po,[A(G(B),{icon:"carbon:warning",class:"warning-icon"}),e("p",null,[s[31]||(s[31]=E("确定要删除 ")),e("strong",null,h((g=i.value)==null?void 0:g.name),1),s[32]||(s[32]=E(" 吗？"))]),s[33]||(s[33]=e("p",{class:"warning-text"},"此操作不可恢复。",-1))])]}),_:1},8,["modelValue"]),t.value?(l(),d("div",{key:0,class:"error-toast",onClick:s[10]||(s[10]=g=>t.value="")},[A(G(B),{icon:"carbon:error"}),E(" "+h(t.value),1)])):V("",!0),_.value?(l(),d("div",{key:1,class:"success-toast",onClick:s[11]||(s[11]=g=>_.value="")},[A(G(B),{icon:"carbon:checkmark"}),E(" "+h(_.value),1)])):V("",!0)])}}}),go=ne(vo,[["__scopeId","data-v-95e61e65"]]);class ve{static async getAllMcpServers(){const o=await fetch(`${this.BASE_URL}/list`);if(!o.ok)throw new Error(`获取 MCP 服务器列表失败: ${o.status}`);return await o.json()}static async addMcpServer(o){try{const t=await fetch(`${this.BASE_URL}/add`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)});if(!t.ok)throw new Error(`添加 MCP 服务器失败: ${t.status}`);return{success:!0,message:"添加 MCP 服务器成功"}}catch(t){return console.error("添加 MCP 服务器失败:",t),{success:!1,message:t instanceof Error?t.message:"添加失败，请重试"}}}static async removeMcpServer(o){try{const t=await fetch(`${this.BASE_URL}/remove?id=${o}`);if(!t.ok)throw new Error(`删除 MCP 服务器失败: ${t.status}`);return{success:!0,message:"删除 MCP 服务器成功"}}catch(t){return console.error(`删除 MCP 服务器[${o}]失败:`,t),{success:!1,message:t instanceof Error?t.message:"删除失败，请重试"}}}}pe(ve,"BASE_URL","/api/mcp");const fo={class:"mcp-config-panel"},ho={class:"mcp-header"},mo={class:"header-left"},yo={class:"mcp-stats"},_o={class:"stat-item"},bo={class:"stat-value"},wo={class:"header-right"},$o={class:"search-box"},ko={key:0,class:"loading-container"},Co={key:1,class:"mcp-layout"},So={class:"mcp-table-container"},To={key:0,class:"empty-state"},Ao={class:"empty-state-text"},xo={key:1,class:"mcp-table-wrapper"},Eo={class:"mcp-table"},Vo={class:"mcp-id"},Mo={class:"mcp-server-name"},Po={class:"server-name-content"},Uo={class:"mcp-connection-type"},Oo={class:"mcp-config"},Bo=["title"],Go={class:"mcp-actions"},Lo=["onClick","disabled"],Io={class:"add-mcp-container"},Ro={class:"mcp-form"},No={class:"mcp-form-group"},Do={class:"connection-type-options"},jo={class:"connection-type-option"},Ko={class:"connection-type-option"},Jo={class:"mcp-form-group"},qo={class:"mcp-form-actions"},zo=["disabled"],Fo=["disabled"],Yo=ae({__name:"mcpConfig",setup(L){const o=O(!1),t=O([]),_=O(""),p=ie({connectionType:"STUDIO",configJson:""}),i=ie({show:!1,text:"",type:"success"});q(()=>p.connectionType==="STUDIO"?`请输入MCP服务器配置JSON。

        例如：
        {
        "mcpServers": {
            "github": {
            "command": "npx",
            "args": [
                "-y",
                "@modelcontextprotocol/server-github"
            ],
            "env": {
                "GITHUB_PERSONAL_ACCESS_TOKEN": "<YOUR_TOKEN>"
            }
            }
        }
        }`:`请输入SSE MCP服务器配置JSON。

例如：
{
  "mcpServers": {
    "remote-server": {
      "url": "https://example.com/mcp",
      "headers": {
        "Authorization": "Bearer <YOUR_TOKEN>"
      }
    }
  }
}`);const S=q(()=>p.configJson.trim().length>0),M=q(()=>{if(!_.value.trim())return t.value;const T=_.value.toLowerCase();return t.value.filter(v=>v.mcpServerName.toLowerCase().includes(T)||v.connectionType.toLowerCase().includes(T)||v.connectionConfig.toLowerCase().includes(T))}),C=T=>T?T.length>50?T.substring(0,50)+"...":T:"",I=(T,v="success")=>{i.text=T,i.type=v,i.show=!0,setTimeout(()=>{i.show=!1},3e3)},x=async()=>{try{o.value=!0,t.value=await ve.getAllMcpServers()}catch(T){console.error("加载MCP服务器列表失败:",T),I("加载MCP服务器列表失败","error")}finally{o.value=!1}},D=async()=>{if(!S.value){I("请输入MCP服务器配置","error");return}try{JSON.parse(p.configJson)}catch{I("配置JSON格式不正确，请检查语法","error");return}try{o.value=!0;const T={connectionType:p.connectionType,configJson:p.configJson},v=await ve.addMcpServer(T);v.success?(I("添加MCP服务器成功"),U(),await x()):I(v.message||"添加MCP服务器失败","error")}catch(T){console.error("添加MCP服务器失败:",T),I("添加MCP服务器失败，请重试","error")}finally{o.value=!1}},ee=async T=>{if(confirm("确定要删除这个MCP服务器配置吗？此操作不可恢复。"))try{o.value=!0;const v=await ve.removeMcpServer(T);v.success?(I("删除MCP服务器成功"),await x()):I(v.message||"删除MCP服务器失败","error")}catch(v){console.error("删除MCP服务器失败:",v),I("删除MCP服务器失败，请重试","error")}finally{o.value=!1}},U=()=>{p.connectionType="STUDIO",p.configJson=""};return ye(()=>{x()}),(T,v)=>(l(),d("div",fo,[e("div",ho,[e("div",mo,[v[5]||(v[5]=e("h2",null,"MCP配置",-1)),e("div",yo,[e("span",_o,[v[4]||(v[4]=e("span",{class:"stat-label"},"总服务器:",-1)),e("span",bo,h(t.value.length),1)])])]),e("div",wo,[e("div",$o,[N(e("input",{"onUpdate:modelValue":v[0]||(v[0]=P=>_.value=P),type:"text",placeholder:"搜索MCP服务器...",class:"search-input"},null,512),[[Y,_.value]]),v[6]||(v[6]=e("span",{class:"search-icon"},"🔍",-1))])])]),o.value?(l(),d("div",ko,v[7]||(v[7]=[e("div",{class:"loading-spinner"},null,-1),e("p",null,"正在加载MCP服务器...",-1)]))):(l(),d("div",Co,[e("div",So,[v[11]||(v[11]=e("h3",{class:"section-title"},"已配置的MCP服务器",-1)),M.value.length===0?(l(),d("div",To,[v[8]||(v[8]=e("div",{class:"empty-state-icon"},"📂",-1)),e("div",Ao,h(_.value?"未找到匹配的MCP服务器":"暂无MCP服务器配置"),1)])):(l(),d("div",xo,[e("table",Eo,[v[10]||(v[10]=e("thead",null,[e("tr",null,[e("th",null,"ID"),e("th",null,"服务器名称"),e("th",null,"连接类型"),e("th",null,"连接配置"),e("th",null,"操作")])],-1)),e("tbody",null,[(l(!0),d(K,null,J(M.value,P=>(l(),d("tr",{key:P.id,class:"mcp-row"},[e("td",Vo,h(P.id),1),e("td",Mo,[e("div",Po,[v[9]||(v[9]=e("span",{class:"server-icon"},"🔌",-1)),E(" "+h(P.mcpServerName),1)])]),e("td",Uo,[e("span",{class:Q(["connection-type-badge",P.connectionType.toLowerCase()])},h(P.connectionType),3)]),e("td",Oo,[e("div",{class:"config-preview",title:P.connectionConfig},h(C(P.connectionConfig)),9,Bo)]),e("td",Go,[e("button",{onClick:H=>ee(P.id),class:"action-btn delete-btn",disabled:o.value}," 删除 ",8,Lo)])]))),128))])])]))]),e("div",Io,[v[16]||(v[16]=e("div",{class:"add-mcp-header"},[e("h3",{class:"add-mcp-title"},"添加MCP服务器")],-1)),e("div",Ro,[e("div",No,[v[14]||(v[14]=e("label",{class:"form-label"},"连接类型：",-1)),e("div",Do,[e("div",jo,[N(e("input",{type:"radio",id:"mcp-connection-type-studio","onUpdate:modelValue":v[1]||(v[1]=P=>p.connectionType=P),value:"STUDIO"},null,512),[[ke,p.connectionType]]),v[12]||(v[12]=e("label",{for:"mcp-connection-type-studio",class:"radio-label"},[e("span",{class:"radio-title"},"STUDIO"),e("span",{class:"connection-type-desc"},"本地mcp server，目前市面上主流的是这个")],-1))]),e("div",Ko,[N(e("input",{type:"radio",id:"mcp-connection-type-sse","onUpdate:modelValue":v[2]||(v[2]=P=>p.connectionType=P),value:"SSE"},null,512),[[ke,p.connectionType]]),v[13]||(v[13]=e("label",{for:"mcp-connection-type-sse",class:"radio-label"},[e("span",{class:"radio-title"},"SSE"),e("span",{class:"connection-type-desc"},"通过http server等提供的，目前网络上比较少见")],-1))])])]),e("div",Jo,[v[15]||(v[15]=e("label",{class:"form-label"},"mcp json配置：",-1)),N(e("textarea",{"onUpdate:modelValue":v[3]||(v[3]=P=>p.configJson=P),placeholder:"请输入MCP服务器的配置(JSON格式)...",class:"config-textarea",rows:"6"},null,512),[[Y,p.configJson]])]),e("div",qo,[e("button",{onClick:D,class:"action-btn add-btn",disabled:o.value}," 添加 ",8,zo),e("button",{onClick:U,class:"action-btn reset-btn",disabled:o.value}," 重置 ",8,Fo)])]),v[17]||(v[17]=Pe('<div class="mcp-form-instructions" data-v-e67db080><h4 data-v-e67db080>使用说明：</h4><ol data-v-e67db080><li data-v-e67db080>找到你要用的mcp server的配置json： <ul class="indented-list" data-v-e67db080><li data-v-e67db080><strong data-v-e67db080>本地(STDIO)</strong>：可以在<a href="https://mcp.so" target="_blank" rel="noopener" data-v-e67db080>mcp.so</a>上找到，需要你有Node.js环境并理解你要配置的json里面的每一个项，做对应调整比如配置ak</li><li data-v-e67db080><strong data-v-e67db080>远程服务(SSE)</strong>：<a href="https://mcp.higress.ai/" target="_blank" rel="noopener" data-v-e67db080>mcp.higress.ai/</a>上可以找到，有SSE和STREAMING两种，目前SSE协议更完备一些</li></ul></li><li data-v-e67db080>将json配置复制到上面的输入框，本地选STUDIO，远程选STREAMING或SSE，提交</li><li data-v-e67db080>这样mcp tools就注册成功了。</li><li data-v-e67db080>然后需要在Agent配置里面，新建一个agent，然后增加指定你刚才添加的mcp tools，这样可以极大减少冲突，增强tools被agent选择的准确性</li></ol></div>',1))])])),A(_e,{name:"message-fade"},{default:oe(()=>[i.show?(l(),d("div",{key:0,class:Q(["message-toast",i.type])},h(i.text),3)):V("",!0)]),_:1})]))}}),Ho=ne(Yo,[["__scopeId","data-v-e67db080"]]),Xo={class:"config-container"},Qo={class:"config-header"},Wo={class:"header-actions"},Zo={class:"config-content"},en={class:"config-nav"},sn=["onClick"],tn={class:"config-details"},on=ae({__name:"index",setup(L){const o=O("basic"),t=[{key:"basic",label:"基础配置",icon:"carbon:settings"},{key:"agent",label:"Agent配置",icon:"carbon:bot"},{key:"mcp",label:"Tools/MCP配置",icon:"carbon:tool-box"}];return(_,p)=>(l(),d("div",Xo,[e("div",Qo,[e("div",Wo,[e("button",{class:"action-btn",onClick:p[0]||(p[0]=i=>_.$router.push("/"))},[A(G(B),{icon:"carbon:arrow-left"}),p[1]||(p[1]=E(" 返回主页 "))])])]),e("div",Zo,[e("nav",en,[(l(),d(K,null,J(t,(i,S)=>e("div",{key:S,class:Q(["nav-item",{active:o.value===i.key}]),onClick:M=>o.value=i.key},[A(G(B),{icon:i.icon,width:"20"},null,8,["icon"]),e("span",null,h(i.label),1)],10,sn)),64))]),e("div",tn,[o.value==="basic"?(l(),re(ot,{key:0})):V("",!0),o.value==="agent"?(l(),re(go,{key:1})):V("",!0),o.value==="mcp"?(l(),re(Ho,{key:2})):V("",!0)])])]))}}),rn=ne(on,[["__scopeId","data-v-bad7e81b"]]);export{rn as default};
