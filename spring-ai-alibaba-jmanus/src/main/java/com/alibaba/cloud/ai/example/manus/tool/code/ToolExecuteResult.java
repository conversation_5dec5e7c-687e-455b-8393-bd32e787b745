/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.cloud.ai.example.manus.tool.code;

public class ToolExecuteResult {

	public ToolExecuteResult() {

	}

	public ToolExecuteResult(String output) {
		setOutput(output);
	}

	public ToolExecuteResult(String output, boolean interrupted) {
		setOutput(output);
		setInterrupted(interrupted);
	}

	/**
	 * 工具返回的内容
	 */
	private String output;

	/**
	 * 是否中断
	 */
	private boolean interrupted;

	public String getOutput() {
		return output;
	}

	void setOutput(String output) {
		this.output = output;
	}

	boolean isInterrupted() {
		return interrupted;
	}

	void setInterrupted(boolean interrupted) {
		this.interrupted = interrupted;
	}

}
