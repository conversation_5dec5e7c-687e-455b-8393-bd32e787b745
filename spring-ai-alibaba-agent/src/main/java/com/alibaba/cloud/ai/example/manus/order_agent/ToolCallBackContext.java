package com.alibaba.cloud.ai.example.manus.order_agent;

import lombok.Data;
import org.springframework.ai.tool.ToolCallback;

@Data
public class ToolCallBackContext {

		private final ToolCallback toolCallback;

		private final ToolCallBiFunctionDef<?> functionInstance;

		public ToolCallBackContext(ToolCallback toolCallback, ToolCallBiFunctionDef<?> functionInstance) {
			this.toolCallback = toolCallback;
			this.functionInstance = functionInstance;
		}


	}