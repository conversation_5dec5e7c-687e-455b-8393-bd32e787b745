package com.alibaba.cloud.ai.example.manus.order_agent;


import com.alibaba.cloud.ai.example.manus.tool.CalcTool;
import com.alibaba.cloud.ai.example.manus.tool.OrderQueryTool;
import com.alibaba.cloud.ai.example.manus.tool.mcp.model.po.McpConfigEntity;
import com.alibaba.cloud.ai.example.manus.tool.mcp.model.po.McpConfigType;
import com.alibaba.cloud.ai.example.manus.tool.mcp.model.vo.McpServerConfig;
import com.alibaba.cloud.ai.example.manus.tool.mcp.model.vo.McpServiceEntity;
import com.alibaba.cloud.ai.example.manus.tool.mcp.model.vo.McpTool;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.modelcontextprotocol.client.McpAsyncClient;
import io.modelcontextprotocol.client.McpClient;
import io.modelcontextprotocol.client.transport.WebFluxSseClientTransport;
import io.modelcontextprotocol.spec.McpClientTransport;
import io.modelcontextprotocol.spec.McpSchema;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.ChatClient.CallResponseSpec;
import org.springframework.ai.chat.client.advisor.SimpleLoggerAdvisor;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.mcp.AsyncMcpToolCallbackProvider;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.function.FunctionToolCallback;
import org.springframework.ai.tool.metadata.ToolMetadata;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.reactive.function.client.WebClient;


@RestController
@RequestMapping("/order")
public class OrderAgent {

  private static final Logger logger = LoggerFactory.getLogger(OrderAgent.class);

  @Autowired
  private ChatModel chatModel;

  @PostMapping("/agent")
  public ChatResponse orderAgent(@RequestBody Map<String, String> request) throws Exception {
    String userPrompt = request.get("prompt");

    List<ToolCallback> callbacks = toolCallbackMap().stream()
        .map(ToolCallBackContext::getToolCallback).toList();
    ChatClient chatClient = ChatClient.builder(chatModel).defaultAdvisors(new SimpleLoggerAdvisor())
        .defaultOptions(OpenAiChatOptions.builder().internalToolExecutionEnabled(true).build())
        .build();

    CallResponseSpec call = chatClient.prompt(userPrompt).toolCallbacks(callbacks).call();
    return call.chatResponse();
  }

  public List<ToolCallBackContext> toolCallbackMap() throws Exception {
    List<ToolCallBackContext> toolCallBackContextList = new ArrayList<>();

    List<ToolCallBiFunctionDef<?>> toolDefinitions = new ArrayList<>();
    // 添加所有工具定义
    toolDefinitions.add(new OrderQueryTool());
    toolDefinitions.add(new CalcTool());

    // 添加MCP工具 DEMO
    McpConfigEntity mcpConfigEntity = new McpConfigEntity();
    mcpConfigEntity.setMcpServerName("orderMcpServerDependency");// 订单依赖的MCP服务器
    mcpConfigEntity.setConnectionType(McpConfigType.SSE);
    mcpConfigEntity.setConnectionConfig("{\"transport\":\"sse\",\"url\":\"http://10.4.1.182:8089/sse?app_id=order-assistant-dify&yxt_operator_context_operator_user_id=123\"}");

    McpClientTransport transport = buildTrans(mcpConfigEntity);
    McpAsyncClient mcpAsyncClient = McpClient.async(transport)
        .clientInfo(new McpSchema.Implementation(mcpConfigEntity.getMcpServerName(), "1.0.0")).build();
    mcpAsyncClient.initialize();
    AsyncMcpToolCallbackProvider callbackProvider = new AsyncMcpToolCallbackProvider(
        mcpAsyncClient);
    McpServiceEntity mcpServiceEntity = new McpServiceEntity(mcpAsyncClient, callbackProvider);

    String serviceGroup = mcpServiceEntity.getServiceGroup();
    ToolCallback[] tCallbacks = mcpServiceEntity.getAsyncMcpToolCallbackProvider()
        .getToolCallbacks();
    for (ToolCallback tCallback : tCallbacks) {
      // 这里的 serviceGroup 是工具的名称
      toolDefinitions.add(new McpTool(tCallback, serviceGroup));
    }

    // 为每个工具创建 FunctionToolCallback
    for (ToolCallBiFunctionDef<?> toolDefinition : toolDefinitions) {
      FunctionToolCallback<?, ToolExecuteResult> functionToolcallback = FunctionToolCallback.builder(
              toolDefinition.getName(), toolDefinition).description(toolDefinition.getDescription())
          .inputSchema(toolDefinition.getParameters()).inputType(toolDefinition.getInputType())
          .toolMetadata(
              ToolMetadata.builder().returnDirect(toolDefinition.isReturnDirect()).build()).build();

      ToolCallBackContext functionToolcallbackContext = new ToolCallBackContext(
          functionToolcallback, toolDefinition);
      toolCallBackContextList.add(functionToolcallbackContext);
    }
    return toolCallBackContextList;
  }

  public McpClientTransport buildTrans(McpConfigEntity mcpConfigEntity) throws Exception {
    McpClientTransport transport = null;
    String serverName = mcpConfigEntity.getMcpServerName();

    try (JsonParser jsonParser = new ObjectMapper().createParser(
        mcpConfigEntity.getConnectionConfig())) {
      McpServerConfig mcpServerConfig = jsonParser.readValueAs(McpServerConfig.class);

      // 验证URL配置
      if (mcpServerConfig.getUrl() == null || mcpServerConfig.getUrl().trim().isEmpty()) {
        throw new IOException("Invalid or missing MCP server URL for server: " + serverName);
      }

      String url = mcpServerConfig.getUrl().trim();
      String baseUrl;
      String sseEndpoint;

      try {
        java.net.URL parsedUrl = new java.net.URL(url);
        baseUrl =
            parsedUrl.getProtocol() + "://" + parsedUrl.getHost() + (parsedUrl.getPort() == -1 ? ""
                : ":" + parsedUrl.getPort());

        // 检查URL路径是否以/sse结尾，如果不是则抛出错误
        String path = parsedUrl.getPath();
        if (path == null || !path.endsWith("/sse")) {
          throw new IllegalArgumentException(
              "URL路径必须以/sse结尾，当前路径: " + path + " for server: " + serverName);
        }

        // 去掉尾部的sse，作为sseEndpoint传入
//        sseEndpoint = path;
        sseEndpoint = parsedUrl.getFile(); // 全路径

        // 移除开头的斜杠，因为WebClient的baseUrl已经包含了域名
        if (sseEndpoint.startsWith("/")) {
          sseEndpoint = sseEndpoint.substring(1);
        }

        // 如果去掉sse后为空，使用默认路径
        if (sseEndpoint.isEmpty()) {
          sseEndpoint = null;
        }
      } catch (java.net.MalformedURLException e) {
        logger.error("Invalid URL format: {} for server: {}", url, serverName, e);
        throw new IllegalArgumentException(
            "Invalid URL format: " + url + " for server: " + serverName, e);
      }

      logger.info("Connecting to base URL: {}, SSE endpoint: {} for server: {}", baseUrl,
          sseEndpoint, serverName);

      // 创建WebClient并添加必要的请求头
      WebClient.Builder webClientBuilder = WebClient.builder().baseUrl(baseUrl)
          .defaultHeader("Accept", "text/event-stream")
          .defaultHeader("Content-Type", "application/json")
          .defaultHeader("User-Agent", "MCP-Client/1.0.0");
      if (sseEndpoint != null && !sseEndpoint.isEmpty()) {
        transport = new WebFluxSseClientTransport(webClientBuilder, new ObjectMapper(),
            sseEndpoint);
      } else {
        transport = new WebFluxSseClientTransport(webClientBuilder, new ObjectMapper());
      }
    }

    return transport;
  }
}
