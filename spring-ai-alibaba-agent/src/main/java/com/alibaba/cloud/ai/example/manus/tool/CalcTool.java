/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.cloud.ai.example.manus.tool;

import com.alibaba.cloud.ai.example.manus.order_agent.ToolCallBiFunctionDef;
import com.alibaba.cloud.ai.example.manus.order_agent.ToolExecuteResult;
import com.alibaba.cloud.ai.example.manus.tool.CalcTool.CalcInput;
import java.math.BigDecimal;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.model.ToolContext;

@Data
public class CalcTool implements ToolCallBiFunctionDef<CalcInput> {

  private static final Logger log = LoggerFactory.getLogger(CalcTool.class);

  /**
   * 内部输入类，用于定义终止工具的输入参数
   */
  @Data
  public static class CalcInput {

    private BigDecimal num1;
    private BigDecimal num2;
    private String operate;

  }

  private static String PARAMETERS = """
      {
        "type" : "object",
        "properties" : {
          "num1" : {
            "type" : "BigDecimal",
            "description" : "第一个数字"
          },
          "num2" : {
            "type" : "BigDecimal",
            "description" : "第二个数字"
          },
          "operate" : {
            "type" : "string",
            "description" : "操作符"
          }
        },
        "required" : [ "num1", "num2", "operate" ]
      }
      """;


  public static final String name = "calc_tool";
  private static final String description = """
      计算工具。用户会输入两个数字(依次赋值给num1,num2)和一个操作符，工具会根据操作符计算两个数字的结果。
      
      映射关系:
      加法: operate = +
      减法: operate = -
      乘法: operate = *
      除法: operate = /
      
      """;


  private String lastTerminationMessage = "";

  private boolean isTerminated = false;

  private String terminationTimestamp = "";


  public ToolExecuteResult run(CalcInput input) {

    BigDecimal num1 = input.getNum1();
    BigDecimal num2 = input.getNum2();
    String operate = input.getOperate();

    if ("-".equals(operate)) {
      return new ToolExecuteResult(num1.subtract(num2).toString());
    } else if ("+".equals(operate)) {
      return new ToolExecuteResult(num1.add(num2).toString());
    } else if ("*".equals(operate)) {
      return new ToolExecuteResult(num1.multiply(num2).toString());
    } else if ("/".equals(operate)) {
      return new ToolExecuteResult(num1.divide(num2).toString());
    } else {
      return new ToolExecuteResult("不支持的操作符");
    }


  }

  @Override
  public ToolExecuteResult apply(CalcInput input, ToolContext toolContext) {
    return run(input);
  }

  @Override
  public String getName() {
    return name;
  }

  @Override
  public String getDescription() {
    return description;
  }

  @Override
  public String getParameters() {
    return PARAMETERS;
  }

  @Override
  public Class<CalcInput> getInputType() {
    return CalcInput.class;
  }

}
