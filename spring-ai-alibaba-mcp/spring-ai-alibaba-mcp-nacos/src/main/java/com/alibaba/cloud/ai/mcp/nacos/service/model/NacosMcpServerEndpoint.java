/*
 * Copyright 2024-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.cloud.ai.mcp.nacos.service.model;

import com.alibaba.nacos.api.ai.model.mcp.McpEndpointInfo;

import java.util.List;

/**
 * <AUTHOR>
 */
public class NacosMcpServerEndpoint {

	private List<McpEndpointInfo> mcpEndpointInfoList;

	private String exportPath;

	private String protocol;

	private String version;

	public NacosMcpServerEndpoint(List<McpEndpointInfo> mcpEndpointInfoList, String exportPath, String protocol,
			String version) {
		this.mcpEndpointInfoList = mcpEndpointInfoList;
		this.exportPath = exportPath;
		this.protocol = protocol;
		this.version = version;
	}

	public List<McpEndpointInfo> getMcpEndpointInfoList() {
		return mcpEndpointInfoList;
	}

	public void setMcpEndpointInfoList(List<McpEndpointInfo> mcpEndpointInfoList) {
		this.mcpEndpointInfoList = mcpEndpointInfoList;
	}

	public String getExportPath() {
		return exportPath;
	}

	public void setExportPath(String exportPath) {
		this.exportPath = exportPath;
	}

	public String getProtocol() {
		return protocol;
	}

	public void setProtocol(String protocol) {
		this.protocol = protocol;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

}
