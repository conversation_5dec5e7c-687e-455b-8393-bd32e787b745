<!--
  ~ Copyright 2024-2025 the original author or authors.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~ https://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.alibaba.cloud.ai</groupId>
    <artifactId>spring-ai-alibaba-bom</artifactId>
    <version>*******-SNAPSHOT</version>

    <packaging>pom</packaging>

    <name>Spring AI Alibaba Bom</name>
    <url>https://github.com/alibaba/spring-ai-alibaba</url>
    <description>Bill of Materials POM (BOM) for the Spring AI Alibaba modules</description>

    <organization>
        <name>Alibaba Cloud Inc.</name>
        <url>https://java2ai.com</url>
    </organization>

    <scm>
        <url>https://github.com/alibaba/spring-ai-alibaba</url>
        <connection>git://github.com/alibaba/spring-ai-alibaba.git</connection>
        <developerConnection>**************:alibaba/spring-ai-alibaba.git</developerConnection>
    </scm>

    <issueManagement>
        <system>Github Issues</system>
        <url>https://github.com/alibaba/spring-ai-alibaba/issues</url>
    </issueManagement>
    <ciManagement>
        <system>Github Actions</system>
        <url>https://github.com/alibaba/spring-ai-alibaba/actions</url>
    </ciManagement>
    <licenses>
        <license>
            <name>Apache 2.0</name>
            <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <developers>
        <developer>
            <id>chickenlj</id>
            <name>Jun Liu</name>
            <email><EMAIL></email>
            <organization>Alibaba Cloud</organization>
            <organizationUrl>https://aliyun.com</organizationUrl>
        </developer>
    </developers>

    <properties>
        <!-- Plugin Versions -->
        <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
        <maven-surefire-plugin.version>3.1.2</maven-surefire-plugin.version>
        <maven-failsafe-plugin.version>3.1.2</maven-failsafe-plugin.version>
        <maven-javadoc-plugin.version>3.5.0</maven-javadoc-plugin.version>
        <maven-source-plugin.version>3.3.0</maven-source-plugin.version>
        <jacoco-maven-plugin.version>0.8.10</jacoco-maven-plugin.version>
        <flatten-maven-plugin.version>1.5.0</flatten-maven-plugin.version>
        <maven-deploy-plugin.version>3.1.1</maven-deploy-plugin.version>
        <asciidoctor-maven-plugin.version>2.2.3</asciidoctor-maven-plugin.version>
        <maven-assembly-plugin.version>3.7.0</maven-assembly-plugin.version>
        <maven-dependency-plugin.version>3.5.0</maven-dependency-plugin.version>
        <maven-site-plugin.version>4.0.0-M13</maven-site-plugin.version>
        <maven-project-info-reports-plugin.version>3.4.5</maven-project-info-reports-plugin.version>
        <maven-jar-plugin.version>3.3.0</maven-jar-plugin.version>
        <spring-javaformat-maven-plugin.version>0.0.39</spring-javaformat-maven-plugin.version>
    </properties>

    <dependencyManagement>
        <dependencies>

            <!-- Spring AI Alibaba Core -->
            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-core</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- Spring AI Alibaba Starter -->
            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-dashscope</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-arms-observation</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-memory</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-nacos2-mcp-client</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-nacos2-mcp-server</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-nacos-mcp-client</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-nacos-mcp-server</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-nacos-prompt</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-nl2sql</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- Spring AI Alibaba Autoconfiguration  -->
            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-autoconfigure-dashscope</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-autoconfigure-arms-observation</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-autoconfigure-memory</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-autoconfigure-nacos2-mcp-client</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-autoconfigure-nacos2-mcp-server</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-autoconfigure-nacos-prompt</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- Spring AI Alibaba Graph Core -->
            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-graph-core</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- Spring AI Alibaba Studio -->
            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-studio</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- Spring AI Alibaba MCP Nacos -->
            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-mcp-nacos2</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- Spring AI Alibaba Community -->

            <!-- Spring AI Alibaba Community Tool Call Plugins -->
            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-tool-calling-common</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-tool-calling-alitranslate</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-tool-calling-amap</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-tool-calling-aliyunaisearch</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-tool-calling-baidumap</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-tool-calling-baidusearch</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-tool-calling-baidutranslate</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-tool-calling-bravesearch</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-tool-calling-jinacrawler</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-tool-calling-firecrawl</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-tool-calling-dingtalk</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-tool-calling-duckduckgo</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-tool-calling-githubtoolkit</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-tool-calling-googletranslate</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-tool-calling-jsonprocessor</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-tool-calling-kuaidi100</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-tool-calling-larksuite</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-tool-calling-microsofttranslate</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-tool-calling-regex</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-tool-calling-sensitivefilter</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-tool-calling-serpapi</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-tool-calling-sinanews</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-tool-calling-time</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-tool-calling-toutiaonews</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-tool-calling-weather</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-tool-calling-youdaotranslate</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-tool-calling-yuque</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-tool-calling-tavilysearch</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-tool-calling-searches</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- Spring AI Alibaba Vector Stores -->
            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-store-analyticdb</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-store-oceanbase</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-store-opensearch</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-store-tair</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- Spring AI Alibaba Chat Memory -->
            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-memory-jdbc</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-memory-redis</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-memory-elasticsearch</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- Spring AI Alibaba Document Parsers -->
            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-document-parser-apache-pdfbox</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-document-parser-bibtex</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-document-parser-bshtml</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-document-parser-directory</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-document-parser-markdown</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-document-parser-multi-modality</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-document-parser-pdf-tables</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-document-parser-tika</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-document-parser-yaml</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- Spring AI Alibaba Document Readers -->
            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-document-reader-arxiv</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-document-reader-bilibili</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-document-reader-chatgpt-data</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-document-reader-elasticsearch</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-document-reader-email</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-document-reader-github</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-document-reader-gitbook</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-document-reader-gitlab</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-document-reader-gpt-repo</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-document-reader-huggingface-fs</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-document-reader-larksuite</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-document-reader-mbox</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-document-reader-mongodb</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-document-reader-mysql</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-document-reader-notion</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-document-reader-obsidian</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-document-reader-onenote</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-document-reader-poi</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-document-reader-tencent-cos</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-document-reader-youtube</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-starter-document-reader-yuque</artifactId>
                <version>${project.version}</version>
            </dependency>

        </dependencies>

    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>io.spring.javaformat</groupId>
                <artifactId>spring-javaformat-maven-plugin</artifactId>
                <version>${spring-javaformat-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <inherited>true</inherited>
                        <goals>
                            <goal>validate</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-site-plugin</artifactId>
                <version>${maven-site-plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <release>${java.version}</release>
                    <compilerArgs>
                        <compilerArg>-parameters</compilerArg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
                <configuration>
                    <argLine>${surefireArgLine}</argLine>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>${maven-jar-plugin.version}</version>
                <configuration>
                    <archive>
                        <manifestEntries>
                            <Implementation-Title>${project.artifactId}</Implementation-Title>
                            <Implementation-Version>${project.version}</Implementation-Version>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <updatePomFile>true</updatePomFile>
                            <flattenMode>ossrh</flattenMode>
                            <pomElements>
                                <distributionManagement>remove</distributionManagement>
                                <dependencyManagement>expand</dependencyManagement>
                                <repositories>remove</repositories>
                                <scm>keep</scm>
                                <url>keep</url>
                                <organization>resolve</organization>
                            </pomElements>
                        </configuration>
                    </execution>
                    <execution>
                        <id>clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.diffplug.spotless</groupId>
                <artifactId>spotless-maven-plugin</artifactId>
                <version>2.44.5</version>
                <configuration>
                    <java>
                        <removeUnusedImports/>
                    </java>
                </configuration>
                <executions>
                    <execution>
                        <id>spotless-apply</id>
                        <goals>
                            <goal>apply</goal>
                        </goals>
                        <phase>process-sources</phase>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>${maven-deploy-plugin.version}</version>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>license</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.mycila</groupId>
                        <artifactId>license-maven-plugin</artifactId>
                        <version>4.1</version>
                        <executions>
                            <execution>
                                <phase>validate</phase>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <properties>
                                <owner>the original author or authors.</owner>
                                <email/>
                                <year>2024</year>
                            </properties>
                            <quiet>true</quiet>
                            <header>HEADER</header>
                            <excludes>
                                <exclude>**/.antlr/**</exclude>
                                <exclude>**/aot.factories</exclude>
                                <exclude>**/.sdkmanrc</exclude>
                                <exclude>**/*.adoc</exclude>
                                <exclude>**/*.puml</exclude>
                                <exclude>**/pom.xml</exclude>
                                <exclude>**/*.properties</exclude>
                                <exclude>**/*.yaml</exclude>
                                <exclude>**/*.yml</exclude>
                                <exclude>**/*.map</exclude>
                                <exclude>**/*.html</exclude>
                                <exclude>**/*.xhtml</exclude>
                                <exclude>**/*.jsp</exclude>
                                <exclude>**/*.js</exclude>
                                <exclude>**/*.css</exclude>
                                <exclude>**/*.txt</exclude>
                                <exclude>**/*.xjb</exclude>
                                <exclude>**/*.ftl</exclude>
                                <exclude>**/*.xsd</exclude>
                                <exclude>**/*.xml</exclude>
                                <exclude>**/*.sh</exclude>
                                <exclude>**/generated/**</exclude>
                                <exclude>**/Dockerfile</exclude>
                            </excludes>
                        </configuration>
                    </plugin>

                </plugins>
            </build>
        </profile>
        <profile>
            <id>javadoc</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <version>${maven-javadoc-plugin.version}</version>
                        <configuration>
                            <excludePackageNames>
                                org.springframework.ai.sample.*,org.springframework.ai.testcontainers.service.connection.*
                            </excludePackageNames>
                            <overview>
                                ${project.basedir}/spring-ai-docs/src/main/javadoc/overview.html
                            </overview>
                            <detectJavaApiLink>false</detectJavaApiLink>
                            <doclint>none</doclint>
                            <!--							<doclint>all,-missing</doclint>-->
                            <quiet>true</quiet>
                        </configuration>
                        <executions>
                            <execution>
                                <id>generate-javadocs</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                            <execution>
                                <id>generate-aggregate-javadocs</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>aggregate</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>integration-tests</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-failsafe-plugin</artifactId>
                        <version>${maven-failsafe-plugin.version}</version>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>integration-test</goal>
                                    <goal>verify</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>test-coverage</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.jacoco</groupId>
                        <artifactId>jacoco-maven-plugin</artifactId>
                        <version>${jacoco-maven-plugin.version}</version>
                        <executions>
                            <execution>
                                <id>prepare-agent</id>
                                <goals>
                                    <goal>prepare-agent</goal>
                                </goals>
                            </execution>
                            <execution>
                                <id>report</id>
                                <goals>
                                    <goal>report</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>release</id>
            <properties>
                <maven-gpg-plugin.version>3.0.1</maven-gpg-plugin.version>
                <nexus-staging-maven-plugin.version>1.7.0</nexus-staging-maven-plugin.version>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-source-plugin</artifactId>
                        <version>${maven-source-plugin.version}</version>
                        <executions>
                            <execution>
                                <phase>package</phase>
                                <goals>
                                    <goal>jar-no-fork</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <version>${maven-javadoc-plugin.version}</version>
                        <configuration>
                            <doclint>all,-missing</doclint>
                        </configuration>
                        <executions>
                            <execution>
                                <phase>package</phase>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>${maven-gpg-plugin.version}</version>
                        <executions>
                            <execution>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>flatten-maven-plugin</artifactId>
                        <version>${flatten-maven-plugin.version}</version>
                        <configuration>
                            <updatePomFile>true</updatePomFile>
                            <flattenMode>resolveCiFriendliesOnly</flattenMode>
                        </configuration>
                        <executions>
                            <execution>
                                <id>flatten</id>
                                <phase>process-resources</phase>
                                <goals>
                                    <goal>flatten</goal>
                                </goals>
                            </execution>
                            <execution>
                                <id>flatten.clean</id>
                                <phase>clean</phase>
                                <goals>
                                    <goal>clean</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
            <distributionManagement>
                <snapshotRepository>
                    <id>sonatype-nexus-snapshots</id>
                    <name>Sonatype Nexus Snapshots</name>
                    <url>https://oss.sonatype.org/content/repositories/snapshots/</url>
                </snapshotRepository>
                <repository>
                    <id>sonatype-nexus-staging</id>
                    <name>Nexus Release Repository</name>
                    <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
                </repository>
            </distributionManagement>
        </profile>
    </profiles>

    <repositories>
        <repository>
            <id>spring-milestones</id>
            <name>Spring Milestones</name>
            <url>https://repo.spring.io/milestone</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

</project>
