#
# Copyright 2024-2025 the original author or authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
name: 🚀 Task
description: Used to create tasks for the community.
title: "[Task] <title>"
labels: ["needs-triage", "kind/task"]
body:
  - type: textarea
    attributes:
      label: Description
      placeholder: A clear and concise description of Task
    validations:
      required: true
  - type: textarea
    attributes:
      label: Task List
      placeholder: |
        -[ ] Task1...
        -[ ] Task2...
        -[ ] Task3...
        -[ ] Task4...
    validations:
      required: false
  - type: markdown
    attributes:
      value: |
        Please read the [Contribution Guideline](https://java2ai.com/docs/developer/contributor-guide/new-contributor-guide_dev) before submitting the PR.
